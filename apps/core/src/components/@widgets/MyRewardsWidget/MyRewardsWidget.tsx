import type { FC } from 'react'
import type { IDynamicallyRenderedContentProps } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'
import type { DynamicallyRenderedMyRewardsConfigType } from '@widgets/MyRewardsWidget/MyRewardsWidget.schema'
import MyRewardsWidgetClient from '@widgets/MyRewardsWidget/MyRewardsWidget.client'

export interface IMyRewardsWidgetProps extends IDynamicallyRenderedContentProps {
  config: DynamicallyRenderedMyRewardsConfigType
}

const MyRewardsWidget: FC<IMyRewardsWidgetProps> = ({ config, ...props }) => {
  return <MyRewardsWidgetClient config={config} {...props} />
}

export default MyRewardsWidget
