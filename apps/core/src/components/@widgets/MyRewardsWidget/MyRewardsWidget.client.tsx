'use client'
import Image from 'next/image'
import { Button } from '@components/Button'
import type { IDynamicallyRenderedContentProps } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'
import { DynamicLink } from '@components/DynamicLink'
import type { DynamicallyRenderedMyRewardsConfigType } from '@widgets/MyRewardsWidget/MyRewardsWidget.schema'
import styles from '@widgets/MyRewardsWidget/MyRewardsWidget.module.scss'
import { useIsMobile } from '@repo/hooks/useMobile'

export interface IMyRewardsWidgetClientProps extends IDynamicallyRenderedContentProps {
  config: DynamicallyRenderedMyRewardsConfigType
}

const MyRewardsWidgetClient = ({ config }: IMyRewardsWidgetClientProps) => {
  const isMobile = useIsMobile()

  if (!config?.rewards || config.rewards.length === 0) return null

  const renderRewardCard = (reward: any, index: number) => {
    const { title, subTitle, backgroundImageSrc, thumbnailSrc, ctaHref, ctaLabel } = reward

    return (
      <div key={index} className={styles.rewardCard} aria-label="Reward Card">
        {backgroundImageSrc ? (
          <Image src={backgroundImageSrc} className={styles.background} quality={100} fill alt="Reward Card background" />
        ) : null}

        {thumbnailSrc ? (
          <div className={styles.thumbnail}>
            <Image src={thumbnailSrc} quality={100} alt={`${title || 'Reward'} thumbnail`} width={68} height={68} />
          </div>
        ) : null}

        <div className={styles.content}>
          <div className={styles.titleContainer}>
            {title ? <p className={styles.title}>{title}</p> : null}
            {subTitle ? <p className={styles.subTitle}>{subTitle}</p> : null}
          </div>

          {ctaLabel ? (
            <div className={styles.ctaButton}>
              <Button
                as={ctaHref ? DynamicLink : 'button'}
                {...(ctaHref ? { href: ctaHref } : {})}
                label={ctaLabel}
                color="primary"
                size={isMobile ? 'sm' : 'md'}
                fullWidth={!isMobile}
                isTruncated
              />
            </div>
          ) : null}
        </div>
      </div>
    )
  }

  return (
    <div className={styles.myRewardsWidget}>
      {config.title && (
        <div className={styles.header}>
          <h2 className={styles.sectionTitle}>{config.title}</h2>
        </div>
      )}
      <div className={styles.rewardCardsContainer} aria-label="My Rewards">
        {config.rewards.map((reward, index) => renderRewardCard(reward, index))}
      </div>
    </div>
  )
}

export default MyRewardsWidgetClient
