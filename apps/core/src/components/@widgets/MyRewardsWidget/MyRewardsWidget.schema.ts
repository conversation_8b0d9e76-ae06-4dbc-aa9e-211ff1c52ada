import { z } from 'zod'
import { DynamicallyRenderedContentBaseConfigSchema } from '@components/DynamicContentRenderer/DynamicContentRenderer.schema'
import { DynamicallyRenderedWidget } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'

const RewardCardItemSchema = z.object({
  title: z.string(),
  subTitle: z.string(),
  backgroundImageSrc: z.string().url().optional(),
  thumbnailSrc: z.string().url().optional(),
  ctaLabel: z.string().optional(),
  ctaHref: z.string().optional(),
  ctaActionKey: z.string().optional(),
})

export const DynamicallyRenderedMyRewardsConfigSchema = DynamicallyRenderedContentBaseConfigSchema.extend({
  component: z.literal(DynamicallyRenderedWidget.MY_REWARDS),
  meta: z.object({
    title: z.string().optional(),
    rewards: z.array(RewardCardItemSchema),
  }),
})

export type DynamicallyRenderedMyRewardsConfigType = z.infer<typeof DynamicallyRenderedMyRewardsConfigSchema>['meta']
