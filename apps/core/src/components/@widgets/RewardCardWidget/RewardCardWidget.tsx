import type { FC } from 'react'
import type { IDynamicallyRenderedContentProps } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'
import RewardCardWidgetClient from '@widgets/RewardCardWidget/RewardCardWidget.client'
import type { DynamicallyRenderedRewardCardConfigType } from '@widgets/RewardCardWidget/RewardCardWidget.schema'

export interface IRewardCardWidgetProps extends IDynamicallyRenderedContentProps {
  config: DynamicallyRenderedRewardCardConfigType
}

const RewardCardWidget: FC<IRewardCardWidgetProps> = ({ config, locale }) => {
  if (!config) return null

  return <RewardCardWidgetClient config={config} locale={locale} />
}

export default RewardCardWidget
