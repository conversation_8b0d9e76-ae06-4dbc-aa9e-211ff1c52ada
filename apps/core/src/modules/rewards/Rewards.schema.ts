import { z } from 'zod'
import { DynamicallyRenderedContentBaseConfigSchema } from '@components/DynamicContentRenderer/DynamicContentRenderer.schema'
import { DynamicallyRenderedModule, LayoutEnum } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'

const LayoutEnumZ = z.nativeEnum(LayoutEnum)

export const DynamicallyRenderedRewardsConfigSchema = DynamicallyRenderedContentBaseConfigSchema.extend({
  component: z.literal(DynamicallyRenderedModule.REWARDS),
  meta: z.object({
    title: z.string().optional(),
    layout: LayoutEnumZ.optional(),
    items: z.array(z.any()).min(1),
  }),
})

export type DynamicallyRenderedRewardsConfigType = z.infer<typeof DynamicallyRenderedRewardsConfigSchema>
export type DynamicallyRenderedRewardsConfigMetaType = DynamicallyRenderedRewardsConfigType['meta']
