import { z } from 'zod'
import { DynamicallyRenderedContentBaseConfigSchema } from '@components/DynamicContentRenderer/DynamicContentRenderer.schema'
import { DynamicallyRenderedModule } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'

export const DynamicallyRenderedRewardsConfigSchema = DynamicallyRenderedContentBaseConfigSchema.extend({
  component: z.literal(DynamicallyRenderedModule.REWARDS),
  meta: z.object({
    title: z.string().optional(),
    rewards: z.array(
      z.object({
        id: z.string(),
        title: z.string(),
        subTitle: z.string(),
        backgroundImageSrc: z.string().url().optional(),
        thumbnailSrc: z.string().url().optional(),
        ctaLabel: z.string().optional(),
        ctaHref: z.string().optional(),
        ctaActionKey: z.string().optional(),
      })
    ),
  }),
})

export type DynamicallyRenderedRewardsConfigType = z.infer<typeof DynamicallyRenderedRewardsConfigSchema>
