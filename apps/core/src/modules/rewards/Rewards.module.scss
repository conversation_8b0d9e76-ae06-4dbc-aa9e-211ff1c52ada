@use '@theme/functions.scss' as *;
@use '@theme/variables.scss' as *;
@use '@theme/mixins.scss' as *;

.container {
  width: 100%;
  padding: calculate-rem(16px);
}

.title {
  font-size: calculate-rem(24px);
  font-weight: 700;
  margin-bottom: calculate-rem(16px);
  color: var(--color-on-surface);

  @media (max-width: $breakpoint-mobile) {
    font-size: calculate-rem(20px);
    margin-bottom: calculate-rem(12px);
  }
}

.scrollContainer {
  width: 100%;
  overflow-x: auto;
  overflow-y: hidden;
  
  /* Hide scrollbar for webkit browsers */
  &::-webkit-scrollbar {
    display: none;
  }
  
  /* Hide scrollbar for Firefox */
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.rewardsGrid {
  display: flex;
  gap: calculate-rem(16px);
  padding-bottom: calculate-rem(8px); /* Add some padding for focus states */

  @media (max-width: $breakpoint-mobile) {
    gap: calculate-rem(12px);
  }

  &.horizontal {
    flex-direction: row;
  }

  &.vertical {
    flex-direction: column;
  }
}

.rewardCard {
  flex: 0 0 auto;
  width: calculate-rem(200px);
  
  @media (max-width: $breakpoint-mobile) {
    width: calculate-rem(180px);
  }
}
