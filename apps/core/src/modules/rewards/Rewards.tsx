import type { FC } from 'react'
import React from 'react'
import type { IDynamicallyRenderedContentProps } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'
import { RewardCardWidget } from '@widgets/RewardCardWidget'
import type { DynamicallyRenderedRewardsConfigType } from '@modules/rewards/Rewards.schema'
import styles from '@modules/rewards/Rewards.module.scss'

export interface IRewardsProps extends IDynamicallyRenderedContentProps {
  config: DynamicallyRenderedRewardsConfigType['meta']
}

const Rewards: FC<IRewardsProps> = ({ config, locale }) => {
  if (!config || !config.rewards || config.rewards.length === 0) {
    return null
  }

  const { title, rewards } = config

  return (
    <div className={styles.container}>
      {title && <h2 className={styles.title}>{title}</h2>}
      <div className={styles.scrollContainer}>
        <div className={styles.rewardsGrid}>
          {rewards.map((reward) => (
            <div key={reward.id} className={styles.rewardCard}>
              <RewardCardWidget config={reward} locale={locale} />
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default Rewards
