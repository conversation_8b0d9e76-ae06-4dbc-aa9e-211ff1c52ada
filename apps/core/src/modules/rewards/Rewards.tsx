import type { FC } from 'react'
import React from 'react'
import type { IDynamicallyRenderedContentProps } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'
import DynamicContentRenderer from '@components/DynamicContentRenderer/DynamicContentRenderer'
import type { DynamicallyRenderedRewardsConfigMetaType } from '@modules/rewards/Rewards.schema'
import styles from '@modules/rewards/Rewards.module.scss'

export interface IRewardsProps extends IDynamicallyRenderedContentProps {
  config: DynamicallyRenderedRewardsConfigMetaType
}

const Rewards: FC<IRewardsProps> = async ({ config, locale }) => {
  if (!config || !Array.isArray(config.items) || config.items.length === 0) {
    return null
  }

  const { title, layout = 'horizontal', items } = config

  const content = await Promise.all(
    items.map(async (item: any, index: number) => (
      <React.Fragment key={item.id || `reward-${index}`}>
        <div className={styles.rewardCard}>
          <DynamicContentRenderer config={item} locale={locale} />
        </div>
      </React.Fragment>
    ))
  )

  return (
    <div className={styles.container}>
      {title && <h2 className={styles.title}>{title}</h2>}
      <div className={styles.scrollContainer}>
        <div className={`${styles.rewardsGrid} ${layout === 'vertical' ? styles.vertical : styles.horizontal}`}>
          {content}
        </div>
      </div>
    </div>
  )
}

export default Rewards
