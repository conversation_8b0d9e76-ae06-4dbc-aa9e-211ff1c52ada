import type { FC } from 'react'
import React from 'react'
import type { IDynamicallyRenderedContentProps, DynamicallyRenderedContainer, LayoutEnum } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'
import DynamicContentRenderer from '@components/DynamicContentRenderer/DynamicContentRenderer'
import type { DynamicallyRenderedContainerConfigType } from '@components/DynamicContentRenderer/DynamicContentRenderer.schema'
import type { DynamicallyRenderedRewardsConfigMetaType } from '@modules/rewards/Rewards.schema'
import styles from '@modules/rewards/Rewards.module.scss'

export interface IRewardsProps extends IDynamicallyRenderedContentProps {
  config: DynamicallyRenderedRewardsConfigMetaType
}

const Rewards: FC<IRewardsProps> = async ({ config, locale }) => {
  if (!config || !Array.isArray(config.items) || config.items.length === 0) {
    return null
  }

  const { title, layout = 'horizontal', items } = config

  // Create a container config to handle the layout and rendering
  const containerConfig = {
    component: 'container' as const,
    meta: {
      layout: layout as any,
      items,
    },
  }

  return (
    <div className={styles.container}>
      {title && <h2 className={styles.title}>{title}</h2>}
      <div className={styles.scrollContainer}>
        <DynamicContentRenderer config={containerConfig} locale={locale} />
      </div>
    </div>
  )
}

export default Rewards
